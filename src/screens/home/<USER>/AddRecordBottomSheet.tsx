import { Button, Text, Textarea } from '@/components';
import { useKeyboardEvents } from '@/hooks/useKeyboardEvents';
import type { BottomSheetBackdropProps } from '@gorhom/bottom-sheet';
import {
  BottomSheetBackdrop,
  BottomSheetModal,
  BottomSheetScrollView,
} from '@gorhom/bottom-sheet';
import { forwardRef, useCallback, useImperativeHandle, useRef } from 'react';
import type { TextInput } from 'react-native';
import Animated, {
  useAnimatedReaction,
  useAnimatedStyle,
  useSharedValue,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import type {
  AddRecordBottomSheetProps,
  AddRecordBottomSheetRef,
} from './AddRecordBottomSheet.types';

const snapPoints = ['80%'];

/**
 * @description 创建记录底部弹窗
 * <AUTHOR>
 * @date 2025-08-28
 */
export const AddRecordBottomSheet = forwardRef<
  AddRecordBottomSheetRef,
  AddRecordBottomSheetProps
>((props, ref) => {
  // const {} = props;

  const keyboardShowing = useSharedValue(false);

  const sheetRef = useRef<BottomSheetModal>(null);
  const inputRef = useRef<TextInput>(null);

  const { bottom: safeBottom, top: safeTop } = useSafeAreaInsets();

  const bottomContainerStyle = useAnimatedStyle(() => {
    return {
      bottom: safeTop,
      position: 'absolute',
      left: 0,
      right: 0,
      paddingLeft: 16,
      paddingRight: 16,
      paddingTop: 12,
      paddingBottom: 12 + (keyboardShowing.value ? 0 : safeBottom),
      backgroundColor: 'white',
    };
  });

  console.log('😅😅😅 royrao: ', { safeBottom });

  const renderBackdrop = useCallback(
    (p: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop {...p} appearsOnIndex={0} disappearsOnIndex={-1} />
    ),
    [],
  );

  const onPressSave = useCallback(() => {}, []);

  const present = useCallback(async () => {
    sheetRef.current?.present();
  }, []);

  const dismiss = useCallback(() => {
    sheetRef.current?.dismiss();
    inputRef.current?.clear();

    keyboardShowing.value = false;
  }, [keyboardShowing]);

  console.log('😅😅😅 royrao: ', { keyboardShowing });

  useKeyboardEvents({
    willShow: () => {
      keyboardShowing.value = true;
    },
    willHide: () => {
      keyboardShowing.value = false;
    },
  });

  useAnimatedReaction(
    () => keyboardShowing.value,
    (value) => {
      console.log('😅😅😅 royrao: ', { value });
    },
  );

  useImperativeHandle(ref, () => ({ present, dismiss }));

  return (
    <BottomSheetModal
      ref={sheetRef}
      name="AddRecordBottomSheet"
      snapPoints={snapPoints}
      enablePanDownToClose
      enableDynamicSizing={false}
      enableContentPanningGesture={false}
      backdropComponent={renderBackdrop}
    >
      <BottomSheetScrollView
        className="flex-1 px-5"
        keyboardDismissMode="interactive"
      >
        <Textarea ref={inputRef} placeholder="请输入" multiline />
      </BottomSheetScrollView>

      <Animated.View style={bottomContainerStyle}>
        <Button onPress={onPressSave}>
          <Text>保存</Text>
        </Button>
      </Animated.View>
    </BottomSheetModal>
  );
});
