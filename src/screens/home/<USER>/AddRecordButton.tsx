import { THEME } from '@/utils/theme';
import { Plus } from 'lucide-react-native';
import type { TouchableOpacityProps } from 'react-native';
import { TouchableOpacity } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

type Props = {
  onPress: TouchableOpacityProps['onPress'];
};

/**
 * @description 创建一条记录
 * <AUTHOR>
 * @date 2025-08-28
 */
export const AddRecordButton = (props: Props) => {
  const { onPress } = props;

  const { bottom: safeBottom } = useSafeAreaInsets();

  return (
    <TouchableOpacity
      className="absolute right-8 rounded-xl bg-primary p-2"
      style={{ bottom: safeBottom + 80 }}
      hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      onPress={onPress}
    >
      <Plus color={THEME.light.primaryForeground} size={32} />
    </TouchableOpacity>
  );
};
