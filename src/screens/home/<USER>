import { HeadingContainer } from '@/components';
import type { BottomSheetModal } from '@gorhom/bottom-sheet';
import { useCallback, useRef } from 'react';
import { Text } from 'react-native';
import { AddRecordBottomSheet } from './add-record/AddRecordBottomSheet';
import { AddRecordButton } from './add-record/AddRecordButton';

export const HomeScreen = () => {
  const bottomSheetRef = useRef<BottomSheetModal>(null);

  const onPressAddRecord = useCallback(() => {
    bottomSheetRef.current?.present();
  }, []);

  return (
    <HeadingContainer className="flex-1 bg-background" header="Today">
      <Text>首页</Text>

      <AddRecordButton onPress={onPressAddRecord} />

      <AddRecordBottomSheet ref={bottomSheetRef} />
    </HeadingContainer>
  );
};
