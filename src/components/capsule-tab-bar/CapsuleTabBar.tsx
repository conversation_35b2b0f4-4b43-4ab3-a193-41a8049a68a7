import { type BottomTabBarProps } from '@react-navigation/bottom-tabs';
import {
  type NavigationRoute,
  type ParamListBase,
} from '@react-navigation/native';
import { isNil } from 'es-toolkit';
import { useCallback } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import { twMerge } from 'tailwind-merge';

type Props = BottomTabBarProps;

export const CapsuleTabBar = (props: Props) => {
  const { state, descriptors, navigation } = props;

  const { bottom: safeBottom } = useSafeAreaInsets();

  const renderTabBarItem = useCallback(
    (route: NavigationRoute<ParamListBase, string>, index: number) => {
      const { options } = descriptors[route.key];
      const tabBarLabel =
        typeof options.tabBarLabel === 'function'
          ? /* TODO: RoyRao - 后续如果要自定义，再完善实现 */
            options.tabBarLabel({} as any)
          : options.tabBarLabel;
      const label = !isNil(tabBarLabel)
        ? tabBarLabel
        : !isNil(options.title)
          ? options.title
          : route.name;

      const isFocused = state.index === index;

      const onPress = () => {
        const event = navigation.emit({
          type: 'tabPress',
          target: route.key,
          canPreventDefault: true,
        });

        if (!isFocused && !event.defaultPrevented) {
          navigation.navigate(route.name);
        }
      };

      const onLongPress = () => {
        navigation.emit({
          type: 'tabLongPress',
          target: route.key,
        });
      };

      return (
        <TouchableOpacity
          accessibilityRole="button"
          accessibilityState={isFocused ? { selected: true } : {}}
          accessibilityLabel={options.tabBarAccessibilityLabel}
          onPress={onPress}
          onLongPress={onLongPress}
          style={styles.tabItem}
          key={index}
        >
          <View
            className={twMerge(isFocused ? 'bg-primary' : 'bg-transparent')}
            style={[styles.capsuleButton]}
          >
            <Text
              className={twMerge(
                'text-sm font-semibold',
                isFocused ? 'text-white' : 'text-muted-foreground',
              )}
            >
              {label}
            </Text>
          </View>
        </TouchableOpacity>
      );
    },
    [descriptors, navigation, state.index],
  );

  return (
    <SafeAreaView
      className="absolute items-center justify-center self-center bg-transparent"
      style={{ bottom: safeBottom }}
      edges={['left', 'right']}
    >
      <View className="bg-card" style={styles.capsuleContainer}>
        {state.routes.map((route, index) => {
          return renderTabBarItem(route, index);
        })}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  capsuleContainer: {
    flexDirection: 'row',
    borderRadius: 30,
    paddingHorizontal: 8,
    paddingVertical: 8,
    shadowColor: '#000000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  tabItem: {
    marginHorizontal: 4,
  },
  capsuleButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
  },
});
