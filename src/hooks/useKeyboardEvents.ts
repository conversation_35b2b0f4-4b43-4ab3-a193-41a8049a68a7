import { useEffect } from 'react';
import {
  type KeyboardEventData,
  KeyboardEvents,
  useKeyboardHandler,
  useKeyboardState,
} from 'react-native-keyboard-controller';
import { runOnJS } from 'react-native-reanimated';

/**
 * 键盘的生命周期状态：
 * - `showing`: 键盘正在弹出
 * - `hiding`: 键盘正在收起
 * - `shown`: 键盘完全弹出
 * - `hidden`: 键盘完全收起
 */
export type KeyboardState = 'showing' | 'hiding' | 'shown' | 'hidden';

export enum KeyboardEventName {
  WillShow = 'keyboardWillShow',
  WillHide = 'keyboardWillHide',
  DidShow = 'keyboardDidShow',
  DidHide = 'keyboardDidHide',
}

type Props = {
  /** 当键盘即将弹出时触发 */
  willShow?: (e: KeyboardEventData) => void;
  /** 当键盘即将收起时触发 */
  willHide?: (e: KeyboardEventData) => void;
  /** 当键盘完全弹出时触发 */
  didShow?: (e: KeyboardEventData) => void;
  /** 当键盘完全收起时触发 */
  didHide?: (e: KeyboardEventData) => void;
  /** 键盘在显示或隐藏的动画过程中，每一帧都会触发 */
  onMove?: (height: number) => void;
  /** 键盘状态发生变化时触发 */
  onStateChange?: (state: KeyboardState, height: number) => void;
};

type Returns = {
  keyboardIsVisible: boolean;
  keyboardHeight: number;
};

/** 用于全面监听键盘生命周期事件 */
export const useKeyboardEvents = (props: Props): Returns => {
  const { willShow, willHide, didShow, didHide, onMove, onStateChange } = props;

  const { isVisible: keyboardIsVisible, height: keyboardHeight } =
    useKeyboardState();

  useEffect(() => {
    const willShowEvent = KeyboardEvents.addListener(
      KeyboardEventName.WillShow,
      (e) => {
        willShow?.(e);
      },
    );

    return () => {
      willShowEvent.remove();
    };
  }, [willShow]);

  useEffect(() => {
    const willHideEvent = KeyboardEvents.addListener(
      KeyboardEventName.WillHide,
      (e) => {
        willHide?.(e);
      },
    );

    return () => {
      willHideEvent.remove();
    };
  }, [willHide]);

  useEffect(() => {
    const didShowEvent = KeyboardEvents.addListener(
      KeyboardEventName.DidShow,
      (e) => {
        didShow?.(e);
      },
    );

    return () => {
      didShowEvent.remove();
    };
  }, [didShow]);

  useEffect(() => {
    const didHideEvent = KeyboardEvents.addListener(
      KeyboardEventName.DidHide,
      (e) => {
        didHide?.(e);
      },
    );

    return () => {
      didHideEvent.remove();
    };
  }, [didHide]);

  useKeyboardHandler({
    /** 在键盘动画开始时触发 */
    onStart: (e) => {
      'worklet';

      if (onStateChange) {
        if (e.height > 0) {
          // 键盘目标高度 > 0，意味着动画是“显示”过程
          runOnJS(onStateChange)('showing', e.height);
        } else {
          // 键盘目标高度为 0，意味着动画是“隐藏”过程
          runOnJS(onStateChange)('hiding', e.height);
        }
      }
    },

    /** 在键盘动画的每一帧触发 */
    onMove: (e) => {
      'worklet';

      if (onMove) {
        runOnJS(onMove)(e.height);
      }
    },

    /** 在键盘动画结束时触发 */
    onEnd: (e) => {
      'worklet';

      if (onStateChange) {
        // 动画结束时键盘高度 > 0，说明键盘已“完全显示”
        if (e.height > 0) {
          runOnJS(onStateChange)('shown', e.height);
        } else {
          // 动画结束时键盘高度为 0，说明键盘已“完全隐藏”
          runOnJS(onStateChange)('hidden', e.height);
        }
      }
    },
  });

  return { keyboardIsVisible, keyboardHeight };
};
