# Resolve react_native_pods.rb with node to allow for hoisting
react_native_pods_raw = Pod::Executable.execute_command('node', ['-p',
  'require.resolve(
    "react-native/scripts/react_native_pods.rb",
    {paths: [process.argv[1]]},
  )', __dir__])

# 强力清理所有可能的 null byte 字符
react_native_pods = react_native_pods_raw.to_s
  .gsub(/\x00/, '')           # 清理十六进制 null byte
  .gsub(/\u0000/, '')         # 清理 Unicode null byte
  .gsub(/\0/, '')             # 清理转义的 null byte
  .gsub(/[\x00-\x1F]/, '')    # 清理所有控制字符
  .strip                      # 清理首尾空白

require react_native_pods

platform :ios, min_ios_version_supported
prepare_react_native_project!

linkage = ENV['USE_FRAMEWORKS']
if linkage != nil
  Pod::UI.puts "Configuring Pod with #{linkage}ally linked Frameworks".green
  use_frameworks! :linkage => linkage.to_sym
end

target 'GrudgeBookApp' do
  config = use_native_modules!

  use_react_native!(
    :path => config[:reactNativePath],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  post_install do |installer|
    # https://github.com/facebook/react-native/blob/main/packages/react-native/scripts/react_native_pods.rb#L197-L202
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      # :ccache_enabled => true
    )
  end
end
